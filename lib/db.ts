import { prisma } from './prisma'
import type { 
  User, 
  Document, 
  Job, 
  Template, 
  A<PERSON>Key, 
  ExtractedData,
  SubscriptionPlan,
  DocumentStatus,
  JobStatus,
  JobType
} from '@/lib/generated/prisma'

// User operations
export const userOperations = {
  async findByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email },
      include: {
        accounts: true,
        sessions: true,
      }
    })
  },

  async create(data: {
    email: string
    name?: string
    image?: string
  }) {
    return prisma.user.create({
      data
    })
  },

  async updateSubscription(userId: string, plan: SubscriptionPlan, stripeCustomerId?: string, stripeSubscriptionId?: string) {
    return prisma.user.update({
      where: { id: userId },
      data: {
        subscriptionPlan: plan,
        stripeCustomerId,
        stripeSubscriptionId,
        subscriptionStatus: 'active'
      }
    })
  },

  async incrementDocumentCount(userId: string) {
    return prisma.user.update({
      where: { id: userId },
      data: {
        monthlyDocumentCount: {
          increment: 1
        }
      }
    })
  }
}

// Document operations
export const documentOperations = {
  async create(data: {
    filename: string
    originalName: string
    mimeType: string
    size: number
    storageUrl: string
    userId: string
  }) {
    return prisma.document.create({
      data
    })
  },

  async updateStatus(documentId: string, status: DocumentStatus) {
    return prisma.document.update({
      where: { id: documentId },
      data: { status }
    })
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.document.findMany({
      where: { userId },
      orderBy: { uploadedAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        jobs: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })
  }
}

// Job operations
export const jobOperations = {
  async create(data: {
    userId: string
    documentId: string
    type: JobType
    templateId?: string
  }) {
    return prisma.job.create({
      data
    })
  },

  async updateStatus(jobId: string, status: JobStatus, error?: string) {
    const updateData: any = { status }
    
    if (status === 'RUNNING') {
      updateData.startedAt = new Date()
    } else if (status === 'COMPLETED' || status === 'FAILED') {
      updateData.completedAt = new Date()
    }
    
    if (error) {
      updateData.error = error
    }

    return prisma.job.update({
      where: { id: jobId },
      data: updateData
    })
  },

  async updateProgress(jobId: string, progress: number) {
    return prisma.job.update({
      where: { id: jobId },
      data: { progress }
    })
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        document: true,
        template: true
      }
    })
  }
}

// Template operations
export const templateOperations = {
  async findPublic() {
    return prisma.template.findMany({
      where: { isPublic: true },
      orderBy: { name: 'asc' }
    })
  },

  async findByUser(userId: string) {
    return prisma.template.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  },

  async create(data: {
    name: string
    description?: string
    documentType: string
    fields: any
    userId: string
  }) {
    return prisma.template.create({
      data
    })
  }
}

// API Key operations
export const apiKeyOperations = {
  async create(data: {
    name: string
    key: string
    userId: string
  }) {
    return prisma.apiKey.create({
      data
    })
  },

  async findByKey(key: string) {
    return prisma.apiKey.findUnique({
      where: { key },
      include: { user: true }
    })
  },

  async findByUser(userId: string) {
    return prisma.apiKey.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  },

  async updateUsage(keyId: string) {
    return prisma.apiKey.update({
      where: { id: keyId },
      data: {
        requestCount: { increment: 1 },
        lastUsedAt: new Date()
      }
    })
  }
}

// Extracted Data operations
export const extractedDataOperations = {
  async create(data: {
    data: any
    confidence?: number
    userId: string
    documentId: string
    jobId: string
  }) {
    return prisma.extractedData.create({
      data
    })
  },

  async validate(id: string, validatedBy: string) {
    return prisma.extractedData.update({
      where: { id },
      data: {
        isValidated: true,
        validatedAt: new Date(),
        validatedBy
      }
    })
  },

  async findByDocument(documentId: string) {
    return prisma.extractedData.findMany({
      where: { documentId },
      orderBy: { createdAt: 'desc' }
    })
  }
}

// Export types for use in other files
export type {
  User,
  Document,
  Job,
  Template,
  ApiKey,
  ExtractedData,
  SubscriptionPlan,
  DocumentStatus,
  JobStatus,
  JobType
}
