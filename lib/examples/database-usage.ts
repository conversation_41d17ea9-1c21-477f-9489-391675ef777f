/**
 * Example usage of the database operations
 * This file demonstrates how to use the Prisma client and helper functions
 * in your Next.js application.
 */

import { prisma } from '@/lib/prisma'
import { 
  userOperations, 
  documentOperations, 
  jobOperations, 
  templateOperations,
  apiKeyOperations,
  extractedDataOperations 
} from '@/lib/db'

// Example 1: User Management
export async function createUserExample() {
  // Create a new user
  const user = await userOperations.create({
    email: '<EMAIL>',
    name: '<PERSON>'
  })

  // Find user by email
  const foundUser = await userOperations.findByEmail('<EMAIL>')
  
  // Update user subscription
  await userOperations.updateSubscription(
    user.id, 
    'PROFESSIONAL', 
    'stripe_customer_id',
    'stripe_subscription_id'
  )

  return user
}

// Example 2: Document Processing Workflow
export async function documentProcessingExample(userId: string) {
  // 1. Create a document record
  const document = await documentOperations.create({
    filename: 'invoice_001.pdf',
    originalName: 'Invoice from Acme Corp.pdf',
    mimeType: 'application/pdf',
    size: 1024000,
    storageUrl: 'https://s3.amazonaws.com/bucket/invoice_001.pdf',
    userId
  })

  // 2. Create a processing job
  const job = await jobOperations.create({
    userId,
    documentId: document.id,
    type: 'EXTRACT',
    templateId: 'invoice-template' // Use pre-built template
  })

  // 3. Update job status as it progresses
  await jobOperations.updateStatus(job.id, 'RUNNING')
  await jobOperations.updateProgress(job.id, 50)

  // 4. Store extracted data
  const extractedData = await extractedDataOperations.create({
    data: {
      invoice_number: 'INV-001',
      date: '2024-01-15',
      vendor_name: 'Acme Corp',
      total_amount: 1250.00
    },
    confidence: 0.95,
    userId,
    documentId: document.id,
    jobId: job.id
  })

  // 5. Complete the job
  await jobOperations.updateStatus(job.id, 'COMPLETED')

  // 6. Update document status
  await documentOperations.updateStatus(document.id, 'COMPLETED')

  return { document, job, extractedData }
}

// Example 3: Template Management
export async function templateExample(userId: string) {
  // Get public templates
  const publicTemplates = await templateOperations.findPublic()

  // Create a custom template
  const customTemplate = await templateOperations.create({
    name: 'Custom Invoice Template',
    description: 'Template for our specific invoice format',
    documentType: 'invoice',
    fields: {
      po_number: { type: 'string', required: true },
      invoice_number: { type: 'string', required: true },
      date: { type: 'date', required: true },
      vendor_name: { type: 'string', required: true },
      total_amount: { type: 'number', required: true }
    },
    userId
  })

  // Get user's templates
  const userTemplates = await templateOperations.findByUser(userId)

  return { publicTemplates, customTemplate, userTemplates }
}

// Example 4: API Key Management
export async function apiKeyExample(userId: string) {
  // Generate a new API key
  const apiKey = await apiKeyOperations.create({
    name: 'Production API Key',
    key: 'unfurl_' + Math.random().toString(36).substring(2, 15),
    userId
  })

  // Authenticate with API key
  const authenticatedKey = await apiKeyOperations.findByKey(apiKey.key)
  
  if (authenticatedKey && authenticatedKey.isActive) {
    // Update usage statistics
    await apiKeyOperations.updateUsage(authenticatedKey.id)
  }

  return apiKey
}

// Example 5: Data Validation Workflow
export async function dataValidationExample(extractedDataId: string, userId: string) {
  // Validate extracted data
  const validatedData = await extractedDataOperations.validate(
    extractedDataId,
    userId
  )

  return validatedData
}

// Example 6: Complex Query with Relations
export async function complexQueryExample(userId: string) {
  // Get user with all related data
  const userWithData = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      documents: {
        include: {
          jobs: {
            include: {
              extractedData: true
            }
          }
        },
        orderBy: { uploadedAt: 'desc' },
        take: 10
      },
      templates: true,
      apiKeys: {
        where: { isActive: true }
      }
    }
  })

  return userWithData
}

// Example 7: Usage Statistics
export async function getUsageStats(userId: string) {
  const stats = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      monthlyDocumentCount: true,
      subscriptionPlan: true,
      _count: {
        select: {
          documents: true,
          jobs: true,
          templates: true,
          apiKeys: true
        }
      }
    }
  })

  return stats
}

// Example 8: Batch Operations
export async function batchOperationsExample() {
  // Use transactions for batch operations
  const result = await prisma.$transaction(async (tx) => {
    // Create multiple templates at once
    const templates = await tx.template.createMany({
      data: [
        {
          name: 'Receipt Template v2',
          documentType: 'receipt',
          isPublic: true,
          fields: { /* template config */ }
        },
        {
          name: 'Form Template',
          documentType: 'form',
          isPublic: true,
          fields: { /* template config */ }
        }
      ]
    })

    // Update multiple jobs
    const updatedJobs = await tx.job.updateMany({
      where: { status: 'PENDING' },
      data: { status: 'CANCELLED' }
    })

    return { templates, updatedJobs }
  })

  return result
}
