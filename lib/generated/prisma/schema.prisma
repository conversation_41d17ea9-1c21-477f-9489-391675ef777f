// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Core application models
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Subscription information
  subscriptionPlan     SubscriptionPlan @default(FREE)
  subscriptionStatus   String? // active, canceled, past_due, etc.
  stripeCustomerId     String?          @unique
  stripeSubscriptionId String?          @unique

  // Usage tracking
  monthlyDocumentCount Int      @default(0)
  lastResetDate        DateTime @default(now())

  // Relations
  accounts      Account[]
  sessions      Session[]
  documents     Document[]
  jobs          Job[]
  templates     Template[]
  apiKeys       ApiKey[]
  extractedData ExtractedData[]

  @@map("users")
}

model Document {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int
  storageUrl   String // URL to file in cloud storage (S3, etc.)
  uploadedAt   DateTime @default(now())

  // Document metadata
  status DocumentStatus @default(UPLOADED)

  // Relations
  userId        String
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobs          Job[]
  extractedData ExtractedData[]

  @@map("documents")
}

model Job {
  id          String    @id @default(cuid())
  status      JobStatus @default(PENDING)
  type        JobType   @default(EXTRACT)
  createdAt   DateTime  @default(now())
  startedAt   DateTime?
  completedAt DateTime?

  // Job configuration
  templateId String?

  // Error handling
  error      String?
  retryCount Int     @default(0)

  // Progress tracking
  progress Int @default(0) // 0-100

  // Relations
  userId        String
  documentId    String
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  document      Document        @relation(fields: [documentId], references: [id], onDelete: Cascade)
  template      Template?       @relation(fields: [templateId], references: [id])
  extractedData ExtractedData[]

  @@map("jobs")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Template configuration
  documentType String // invoice, receipt, form, etc.
  fields       Json // JSON configuration for extraction fields
  isPublic     Boolean @default(false) // Pre-built templates vs user templates

  // Relations
  userId String? // null for pre-built templates
  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobs   Job[]

  @@map("templates")
}

model ApiKey {
  id         String    @id @default(cuid())
  name       String // User-friendly name for the key
  key        String    @unique // The actual API key
  createdAt  DateTime  @default(now())
  lastUsedAt DateTime?
  isActive   Boolean   @default(true)

  // Usage tracking
  requestCount Int @default(0)

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model ExtractedData {
  id         String   @id @default(cuid())
  data       Json // The extracted data in JSON format
  confidence Float? // AI confidence score (0-1)
  createdAt  DateTime @default(now())

  // Validation status
  isValidated Boolean   @default(false)
  validatedAt DateTime?
  validatedBy String? // User ID who validated

  // Relations
  userId     String
  documentId String
  jobId      String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  job        Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@map("extracted_data")
}

// Enums
enum SubscriptionPlan {
  FREE
  STARTER
  PROFESSIONAL
  ENTERPRISE
}

enum DocumentStatus {
  UPLOADED
  PROCESSING
  COMPLETED
  FAILED
}

enum JobStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum JobType {
  EXTRACT
  VALIDATE
  EXPORT
}
