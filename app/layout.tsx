import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'Unfurl - Intelligent Data Extraction, Simplified.',
    description:
        'Unfurl is the AI-powered platform that ends manual data entry. Extract structured data from invoices, receipts, and forms with incredible accuracy.',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body className={inter.className}>{children}</body>
        </html>
    );
}
