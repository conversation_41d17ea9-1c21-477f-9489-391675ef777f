# Database Setup - Unfurl

This directory contains the Prisma schema and database configuration for the Unfurl application.

## Database Schema

The database schema includes the following models:

### Authentication (Clerk-based)
- **User**: Core user information with credit-based subscription details (uses Clerk user ID)

### Core Application Models
- **Document**: Uploaded documents with metadata and credit tracking
- **Job**: Background processing jobs for document extraction with credit consumption
- **Template**: Data extraction templates (both user-created and pre-built)
- **ApiKey**: API keys for developer access
- **ExtractedData**: Results from AI data extraction
- **CreditTransaction**: Credit usage and purchase history

### Enums
- **SubscriptionPlan**: FREE (50 credits), STARTER (500 credits), PROFESSIONAL (2000 credits), ENTERPRISE (10000+ credits)
- **DocumentStatus**: UPLOADED, PROCESSING, COMPLETED, FAILED
- **JobStatus**: PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
- **JobType**: EXTRACT, VALIDATE, EXPORT
- **CreditTransactionType**: <PERSON>URCHASE, S<PERSON><PERSON><PERSON><PERSON><PERSON>ON, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BONUS, ROLL<PERSON>VER

## Setup Instructions

1. **Install Dependencies** (already done if you ran npm install)
   ```bash
   npm install prisma @prisma/client pg @types/pg
   ```

2. **Configure Database URL**
   Update the `DATABASE_URL` in your `.env` file:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/unfurl_dev?schema=public"
   ```

3. **Generate Prisma Client**
   ```bash
   npm run db:generate
   ```

4. **Push Schema to Database** (for development)
   ```bash
   npm run db:push
   ```

   Or create and run migrations (for production):
   ```bash
   npm run db:migrate
   ```

5. **Seed the Database** (optional)
   ```bash
   npm run db:seed
   ```

6. **Open Prisma Studio** (optional - database GUI)
   ```bash
   npm run db:studio
   ```

## Available Scripts

- `npm run db:generate` - Generate Prisma Client
- `npm run db:push` - Push schema changes to database (development)
- `npm run db:migrate` - Create and run migrations (production)
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with initial data

## Usage in Code

Import the Prisma client:

```typescript
import { prisma } from '@/lib/prisma'

// Example: Get user by email
const user = await prisma.user.findUnique({
  where: { email: '<EMAIL>' }
})
```

Or use the helper functions:

```typescript
import { userOperations, creditOperations } from '@/lib/db'

// Example: Get user by Clerk ID with credit transactions
const user = await userOperations.findById('clerk_user_123')

// Example: Check credit balance
const balance = await userOperations.checkCreditBalance('clerk_user_123')

// Example: Consume credits for processing
await creditOperations.consumeCredits(
  'clerk_user_123',
  5,
  'Document processing',
  jobId,
  documentId
)
```

## Pre-built Templates

The seed script creates three pre-built templates:
- **Invoice Template**: For extracting data from invoices
- **Receipt Template**: For extracting data from receipts
- **Business Card Template**: For extracting contact information

## Credit System Features

- **Real-time Balance Tracking**: Users can view current credit balance and usage
- **Transaction History**: Complete audit trail of all credit transactions
- **Monthly Reset**: Credits refresh monthly based on subscription plan
- **Rollover Support**: Unused credits roll over (up to 2x monthly limit)
- **Usage Analytics**: Detailed breakdown of credit consumption by operation

## Security Notes

- All user data is properly isolated by userId (Clerk user ID)
- API keys are hashed and stored securely
- Cascade deletes ensure data consistency
- Environment variables are used for sensitive configuration
- Credit transactions are atomic to prevent race conditions
