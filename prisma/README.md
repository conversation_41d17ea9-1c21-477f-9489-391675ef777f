# Database Setup - Unfurl

This directory contains the Prisma schema and database configuration for the Unfurl application.

## Database Schema

The database schema includes the following models:

### Authentication Models (NextAuth.js compatible)
- **Account**: OAuth account information
- **Session**: User session data
- **VerificationToken**: Email verification tokens
- **User**: Core user information with subscription details

### Core Application Models
- **Document**: Uploaded documents with metadata
- **Job**: Background processing jobs for document extraction
- **Template**: Data extraction templates (both user-created and pre-built)
- **ApiKey**: API keys for developer access
- **ExtractedData**: Results from AI data extraction

### Enums
- **SubscriptionPlan**: FREE, STARTER, PROF<PERSON>SIONAL, ENTERPRISE
- **DocumentStatus**: UPLOADED, PROCESSING, COMPLETED, FAILED
- **JobStatus**: PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
- **JobType**: EXTRACT, VALIDATE, EXPORT

## Setup Instructions

1. **Install Dependencies** (already done if you ran npm install)
   ```bash
   npm install prisma @prisma/client pg @types/pg
   ```

2. **Configure Database URL**
   Update the `DATABASE_URL` in your `.env` file:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/unfurl_dev?schema=public"
   ```

3. **Generate Prisma Client**
   ```bash
   npm run db:generate
   ```

4. **Push Schema to Database** (for development)
   ```bash
   npm run db:push
   ```

   Or create and run migrations (for production):
   ```bash
   npm run db:migrate
   ```

5. **Seed the Database** (optional)
   ```bash
   npm run db:seed
   ```

6. **Open Prisma Studio** (optional - database GUI)
   ```bash
   npm run db:studio
   ```

## Available Scripts

- `npm run db:generate` - Generate Prisma Client
- `npm run db:push` - Push schema changes to database (development)
- `npm run db:migrate` - Create and run migrations (production)
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with initial data

## Usage in Code

Import the Prisma client:

```typescript
import { prisma } from '@/lib/prisma'

// Example: Get user by email
const user = await prisma.user.findUnique({
  where: { email: '<EMAIL>' }
})
```

Or use the helper functions:

```typescript
import { userOperations } from '@/lib/db'

// Example: Get user by email with relations
const user = await userOperations.findByEmail('<EMAIL>')
```

## Pre-built Templates

The seed script creates three pre-built templates:
- **Invoice Template**: For extracting data from invoices
- **Receipt Template**: For extracting data from receipts  
- **Business Card Template**: For extracting contact information

## Security Notes

- All user data is properly isolated by userId
- API keys are hashed and stored securely
- Cascade deletes ensure data consistency
- Environment variables are used for sensitive configuration
