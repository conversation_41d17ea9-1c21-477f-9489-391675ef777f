// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Clerk authentication - no additional models needed as <PERSON> handles auth externally

// Core application models
model User {
  id        String   @id @default(cuid()) // This will be the Clerk user ID
  email     String   @unique
  name      String?
  imageUrl  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Subscription information
  subscriptionPlan     SubscriptionPlan @default(FREE)
  subscriptionStatus   String?          @default("active") // active, canceled, past_due, etc.
  stripeCustomerId     String?          @unique
  stripeSubscriptionId String?          @unique

  // Credit-based usage tracking
  creditBalance      Int      @default(50) // Current available credits
  totalCreditsUsed   Int      @default(0) // Lifetime credits consumed
  monthlyCreditsUsed Int      @default(0) // Credits used this billing period
  lastCreditReset    DateTime @default(now()) // Last time monthly credits were reset

  // Credit allocation per plan (refreshed monthly)
  monthlyCreditsLimit Int @default(50) // Based on subscription plan

  // Relations
  documents          Document[]
  jobs               Job[]
  templates          Template[]
  apiKeys            ApiKey[]
  extractedData      ExtractedData[]
  creditTransactions CreditTransaction[]

  @@map("users")
}

model Document {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int
  storageUrl   String // URL to file in cloud storage (S3, etc.)
  uploadedAt   DateTime @default(now())

  // Document metadata
  status DocumentStatus @default(UPLOADED)

  // Credit tracking
  creditsUsed Int @default(0) // Credits consumed for processing this document

  // Relations
  userId            String
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobs              Job[]
  extractedData     ExtractedData[]
  CreditTransaction CreditTransaction[]

  @@map("documents")
}

model Job {
  id          String    @id @default(cuid())
  status      JobStatus @default(PENDING)
  type        JobType   @default(EXTRACT)
  createdAt   DateTime  @default(now())
  startedAt   DateTime?
  completedAt DateTime?

  // Job configuration
  templateId String?

  // Error handling
  error      String?
  retryCount Int     @default(0)

  // Progress tracking
  progress Int @default(0) // 0-100

  // Credit tracking
  creditsUsed Int @default(0) // Credits consumed for this job

  // Relations
  userId            String
  documentId        String
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  document          Document            @relation(fields: [documentId], references: [id], onDelete: Cascade)
  template          Template?           @relation(fields: [templateId], references: [id])
  extractedData     ExtractedData[]
  CreditTransaction CreditTransaction[]

  @@map("jobs")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Template configuration
  documentType String // invoice, receipt, form, etc.
  fields       Json // JSON configuration for extraction fields
  isPublic     Boolean @default(false) // Pre-built templates vs user templates

  // Relations
  userId String? // null for pre-built templates
  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobs   Job[]

  @@map("templates")
}

model ApiKey {
  id         String    @id @default(cuid())
  name       String // User-friendly name for the key
  key        String    @unique // The actual API key
  createdAt  DateTime  @default(now())
  lastUsedAt DateTime?
  isActive   Boolean   @default(true)

  // Usage tracking
  requestCount Int @default(0)

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model ExtractedData {
  id         String   @id @default(cuid())
  data       Json // The extracted data in JSON format
  confidence Float? // AI confidence score (0-1)
  createdAt  DateTime @default(now())

  // Validation status
  isValidated Boolean   @default(false)
  validatedAt DateTime?
  validatedBy String? // User ID who validated

  // Relations
  userId     String
  documentId String
  jobId      String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  job        Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@map("extracted_data")
}

model CreditTransaction {
  id          String                @id @default(cuid())
  amount      Int // Positive for credits added, negative for credits consumed
  type        CreditTransactionType
  description String? // Human-readable description of the transaction
  createdAt   DateTime              @default(now())

  // Related operation details
  jobId      String? // If credits were used for a job
  documentId String? // If credits were used for document processing

  // Metadata
  metadata Json? // Additional transaction details

  // Relations
  userId   String
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  job      Job?      @relation(fields: [jobId], references: [id])
  document Document? @relation(fields: [documentId], references: [id])

  @@map("credit_transactions")
}

// Enums
enum SubscriptionPlan {
  FREE
  STARTER
  PROFESSIONAL
  ENTERPRISE
}

enum DocumentStatus {
  UPLOADED
  PROCESSING
  COMPLETED
  FAILED
}

enum JobStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum JobType {
  EXTRACT
  VALIDATE
  EXPORT
}

enum CreditTransactionType {
  PURCHASE // Credits purchased via Stripe
  SUBSCRIPTION // Monthly credit allocation
  CONSUMPTION // Credits used for operations
  REFUND // Credits refunded
  BONUS // Promotional credits
  ROLLOVER // Credits rolled over from previous month
}
